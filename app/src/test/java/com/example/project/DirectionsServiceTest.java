package com.example.project;

import android.content.Context;

import com.example.project.services.DirectionsService;
import com.google.android.gms.maps.model.LatLng;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for DirectionsService
 */
@RunWith(MockitoJUnitRunner.class)
public class DirectionsServiceTest {

    @Mock
    Context mockContext;

    private DirectionsService directionsService;

    @Before
    public void setUp() {
        // Mock ConfigManager to return empty API key (will use mock data)
        directionsService = new DirectionsService(mockContext);
    }

    @Test
    public void testGeocodeAddress_Toronto() {
        // Test geocoding a Toronto address
        String address = "123 Main Street, Toronto, ON";
        
        directionsService.geocodeAddress(address, new DirectionsService.GeocodeCallback() {
            @Override
            public void onSuccess(LatLng location) {
                // Should return a Toronto-area location
                assertTrue("Latitude should be in Toronto area", 
                    location.latitude > 43.0 && location.latitude < 44.0);
                assertTrue("Longitude should be in Toronto area", 
                    location.longitude > -80.0 && location.longitude < -79.0);
            }
            
            @Override
            public void onError(String error) {
                fail("Geocoding should not fail for Toronto address: " + error);
            }
        });
    }

    @Test
    public void testGeocodeAddress_NewYork() {
        // Test geocoding a New York address
        String address = "123 Broadway, New York, NY";
        
        directionsService.geocodeAddress(address, new DirectionsService.GeocodeCallback() {
            @Override
            public void onSuccess(LatLng location) {
                // Should return a New York area location
                assertTrue("Latitude should be in New York area", 
                    location.latitude > 40.0 && location.latitude < 41.0);
                assertTrue("Longitude should be in New York area", 
                    location.longitude > -75.0 && location.longitude < -73.0);
            }
            
            @Override
            public void onError(String error) {
                fail("Geocoding should not fail for New York address: " + error);
            }
        });
    }

    @Test
    public void testGetDirections_MockData() {
        // Test getting directions with mock data
        LatLng origin = new LatLng(43.6532, -79.3832); // Toronto
        LatLng destination = new LatLng(43.6542, -79.3842); // Nearby location
        
        directionsService.getDirections(origin, destination, new DirectionsService.DirectionsCallback() {
            @Override
            public void onSuccess(DirectionsService.DirectionsResult result) {
                // Verify the result contains expected data
                assertNotNull("Route points should not be null", result.getRoutePoints());
                assertFalse("Route points should not be empty", result.getRoutePoints().isEmpty());
                assertNotNull("Distance should not be null", result.getDistance());
                assertNotNull("Duration should not be null", result.getDuration());
                
                // Verify route contains origin and destination
                assertTrue("Route should contain origin", 
                    result.getRoutePoints().contains(origin));
                assertTrue("Route should contain destination", 
                    result.getRoutePoints().contains(destination));
            }
            
            @Override
            public void onError(String error) {
                fail("Directions should not fail with mock data: " + error);
            }
        });
    }

    @Test
    public void testDirectionsResult_Properties() {
        // Test DirectionsResult class
        LatLng point1 = new LatLng(43.6532, -79.3832);
        LatLng point2 = new LatLng(43.6542, -79.3842);
        
        java.util.List<LatLng> routePoints = new java.util.ArrayList<>();
        routePoints.add(point1);
        routePoints.add(point2);
        
        DirectionsService.DirectionsResult result = new DirectionsService.DirectionsResult(
            routePoints, "2.5 km", "8 min", "test_polyline"
        );
        
        assertEquals("Route points should match", routePoints, result.getRoutePoints());
        assertEquals("Distance should match", "2.5 km", result.getDistance());
        assertEquals("Duration should match", "8 min", result.getDuration());
        assertEquals("Polyline should match", "test_polyline", result.getPolyline());
    }
}
