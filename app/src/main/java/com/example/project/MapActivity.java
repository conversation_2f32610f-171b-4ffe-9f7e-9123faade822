package com.example.project;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.location.Location;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.maps.CameraUpdateFactory;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.OnMapReadyCallback;
import com.google.android.gms.maps.SupportMapFragment;
import com.google.android.gms.maps.model.BitmapDescriptorFactory;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.LatLngBounds;
import com.google.android.gms.maps.model.Marker;
import com.google.android.gms.maps.model.MarkerOptions;
import com.google.android.gms.maps.model.Polyline;
import com.google.android.gms.maps.model.PolylineOptions;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;

import com.example.project.services.DirectionsService;

import java.util.List;

public class MapActivity extends AppCompatActivity implements OnMapReadyCallback {

    private static final String TAG = "MapActivity";
    private static final int LOCATION_PERMISSION_REQUEST_CODE = 1001;

    // UI Components
    private MaterialToolbar toolbar;
    private View loadingOverlay;
    private View errorOverlay;
    private TextView errorMessage;
    private MaterialButton retryButton;
    private MaterialCardView infoCard;
    private TextView restaurantNameText;
    private TextView restaurantAddressText;
    private TextView routeDistanceText;
    private TextView routeDurationText;
    private View routeInfoLayout;

    // Map Components
    private GoogleMap googleMap;
    private SupportMapFragment mapFragment;
    private FusedLocationProviderClient fusedLocationClient;

    // Data
    private String restaurantName;
    private String restaurantAddress;
    private LatLng restaurantLocation;
    private LatLng userLocation;
    private Marker userMarker;
    private Marker restaurantMarker;
    private Polyline routePolyline;

    // Services
    private Handler mainHandler;
    private DirectionsService directionsService;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_map);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.map_coordinator_layout), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        // Initialize services
        fusedLocationClient = LocationServices.getFusedLocationProviderClient(this);
        mainHandler = new Handler(Looper.getMainLooper());
        directionsService = new DirectionsService(this);

        // Get restaurant data from intent
        getRestaurantDataFromIntent();

        // Initialize views
        initializeViews();

        // Setup toolbar
        setupToolbar();

        // Initialize map
        initializeMap();
    }

    private void getRestaurantDataFromIntent() {
        Intent intent = getIntent();
        restaurantName = intent.getStringExtra("restaurant_name");
        restaurantAddress = intent.getStringExtra("restaurant_address");

        if (restaurantName == null) restaurantName = "Restaurant";
        if (restaurantAddress == null) restaurantAddress = "Address not available";

        Log.d(TAG, "Restaurant: " + restaurantName + " at " + restaurantAddress);
    }

    private void initializeViews() {
        toolbar = findViewById(R.id.toolbar);
        loadingOverlay = findViewById(R.id.loading_overlay);
        errorOverlay = findViewById(R.id.error_overlay);
        errorMessage = findViewById(R.id.error_message);
        retryButton = findViewById(R.id.retry_button);
        infoCard = findViewById(R.id.info_card);
        restaurantNameText = findViewById(R.id.restaurant_name);
        restaurantAddressText = findViewById(R.id.restaurant_address);
        routeDistanceText = findViewById(R.id.route_distance);
        routeDurationText = findViewById(R.id.route_duration);
        routeInfoLayout = findViewById(R.id.route_info_layout);

        // Set restaurant info
        restaurantNameText.setText(restaurantName);
        restaurantAddressText.setText(restaurantAddress);

        // Setup retry button
        retryButton.setOnClickListener(v -> {
            hideError();
            initializeMap();
        });
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
        }

        toolbar.setNavigationOnClickListener(v -> {
            finish();
        });
    }

    private void initializeMap() {
        showLoading("Loading map...");

        mapFragment = (SupportMapFragment) getSupportFragmentManager()
                .findFragmentById(R.id.map_fragment);

        if (mapFragment != null) {
            mapFragment.getMapAsync(this);
        } else {
            showError("Failed to load map", "Map fragment not found.");
        }
    }

    @Override
    public void onMapReady(@NonNull GoogleMap map) {
        googleMap = map;
        Log.d(TAG, "Map is ready");

        // Configure map
        configureMap();

        // Check location permissions and get user location
        checkLocationPermissionsAndGetLocation();
    }

    private void configureMap() {
        if (googleMap == null) return;

        // Enable map controls
        googleMap.getUiSettings().setZoomControlsEnabled(true);
        googleMap.getUiSettings().setCompassEnabled(true);
        googleMap.getUiSettings().setMapToolbarEnabled(false);

        // Set map type
        googleMap.setMapType(GoogleMap.MAP_TYPE_NORMAL);
    }

    private void checkLocationPermissionsAndGetLocation() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) 
                == PackageManager.PERMISSION_GRANTED) {
            // Permission granted, get location
            getCurrentLocation();
        } else {
            // Request permission
            ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.ACCESS_FINE_LOCATION},
                    LOCATION_PERMISSION_REQUEST_CODE);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == LOCATION_PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "Location permission granted");
                getCurrentLocation();
            } else {
                Log.d(TAG, "Location permission denied");
                // Continue without user location, just show restaurant
                showRestaurantOnly();
            }
        }
    }

    private void getCurrentLocation() {
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) 
                != PackageManager.PERMISSION_GRANTED) {
            return;
        }

        showLoading("Getting your location...");

        fusedLocationClient.getLastLocation()
                .addOnSuccessListener(this, location -> {
                    if (location != null) {
                        userLocation = new LatLng(location.getLatitude(), location.getLongitude());
                        Log.d(TAG, "User location: " + userLocation.latitude + ", " + userLocation.longitude);
                        
                        // Geocode restaurant address and show both locations
                        geocodeRestaurantAddress();
                    } else {
                        Log.w(TAG, "Last known location is null");
                        showRestaurantOnly();
                    }
                })
                .addOnFailureListener(this, e -> {
                    Log.e(TAG, "Failed to get location", e);
                    showRestaurantOnly();
                });
    }

    private void geocodeRestaurantAddress() {
        showLoading("Finding restaurant location...");

        // Use DirectionsService to geocode the restaurant address
        directionsService.geocodeAddress(restaurantAddress, new DirectionsService.GeocodeCallback() {
            @Override
            public void onSuccess(LatLng location) {
                restaurantLocation = location;
                Log.d(TAG, "Restaurant geocoded to: " + location.latitude + ", " + location.longitude);
                mainHandler.post(() -> showBothLocations());
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "Geocoding failed: " + error);
                // Fallback to a default location
                if (userLocation != null) {
                    restaurantLocation = new LatLng(
                        userLocation.latitude + 0.01,
                        userLocation.longitude + 0.01
                    );
                } else {
                    restaurantLocation = new LatLng(43.6532, -79.3832);
                }
                mainHandler.post(() -> showBothLocations());
            }
        });
    }

    private void showRestaurantOnly() {
        showLoading("Finding restaurant location...");
        
        // Default restaurant location (Toronto)
        restaurantLocation = new LatLng(43.6532, -79.3832);
        
        mainHandler.postDelayed(() -> {
            addRestaurantMarker();
            centerMapOnRestaurant();
            hideLoading();
            showInfoCard();
        }, 500);
    }

    private void showBothLocations() {
        if (googleMap == null) return;

        // Add markers
        addUserLocationMarker();
        addRestaurantMarker();

        // Fit both locations in view
        fitBothLocationsInView();

        // Simulate route calculation
        calculateAndShowRoute();

        hideLoading();
        showInfoCard();
    }

    private void addUserLocationMarker() {
        if (googleMap == null || userLocation == null) return;

        userMarker = googleMap.addMarker(new MarkerOptions()
                .position(userLocation)
                .title("Your Location")
                .icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_BLUE)));
    }

    private void addRestaurantMarker() {
        if (googleMap == null || restaurantLocation == null) return;

        restaurantMarker = googleMap.addMarker(new MarkerOptions()
                .position(restaurantLocation)
                .title(restaurantName)
                .snippet(restaurantAddress)
                .icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_RED)));
    }

    private void centerMapOnRestaurant() {
        if (googleMap == null || restaurantLocation == null) return;

        googleMap.animateCamera(CameraUpdateFactory.newLatLngZoom(restaurantLocation, 15f));
    }

    private void fitBothLocationsInView() {
        if (googleMap == null || userLocation == null || restaurantLocation == null) return;

        LatLngBounds.Builder builder = new LatLngBounds.Builder();
        builder.include(userLocation);
        builder.include(restaurantLocation);

        LatLngBounds bounds = builder.build();
        int padding = 200; // padding in pixels

        googleMap.animateCamera(CameraUpdateFactory.newLatLngBounds(bounds, padding));
    }

    private void calculateAndShowRoute() {
        if (userLocation == null || restaurantLocation == null) return;

        showLoading("Calculating route...");

        // Use DirectionsService to get real route data
        directionsService.getDirections(userLocation, restaurantLocation, new DirectionsService.DirectionsCallback() {
            @Override
            public void onSuccess(DirectionsService.DirectionsResult result) {
                Log.d(TAG, "Route calculated successfully");
                mainHandler.post(() -> {
                    drawRouteFromResult(result);
                    showRouteInfo(result.getDistance(), result.getDuration());
                    hideLoading();
                });
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "Route calculation failed: " + error);
                mainHandler.post(() -> {
                    // Fallback to simple straight line
                    drawSimpleRoute();
                    showRouteInfo("-- km", "-- min");
                    hideLoading();
                });
            }
        });
    }

    private void drawRouteFromResult(DirectionsService.DirectionsResult result) {
        if (googleMap == null || result.getRoutePoints().isEmpty()) return;

        // Clear existing route
        if (routePolyline != null) {
            routePolyline.remove();
        }

        // Draw the route using the points from DirectionsService
        PolylineOptions polylineOptions = new PolylineOptions()
                .width(8f)
                .color(Color.BLUE)
                .geodesic(true);

        // Add all route points
        for (LatLng point : result.getRoutePoints()) {
            polylineOptions.add(point);
        }

        routePolyline = googleMap.addPolyline(polylineOptions);
    }

    private void drawSimpleRoute() {
        if (googleMap == null || userLocation == null || restaurantLocation == null) return;

        // Clear existing route
        if (routePolyline != null) {
            routePolyline.remove();
        }

        // Draw a simple straight line route as fallback
        PolylineOptions polylineOptions = new PolylineOptions()
                .add(userLocation)
                .add(restaurantLocation)
                .width(8f)
                .color(Color.BLUE)
                .geodesic(true);

        routePolyline = googleMap.addPolyline(polylineOptions);
    }

    private void showRouteInfo(String distance, String duration) {
        routeDistanceText.setText(distance);
        routeDurationText.setText(duration);
        routeInfoLayout.setVisibility(View.VISIBLE);
    }

    private void showLoading(String message) {
        TextView loadingText = findViewById(R.id.loading_text);
        loadingText.setText(message);
        loadingOverlay.setVisibility(View.VISIBLE);
        errorOverlay.setVisibility(View.GONE);
    }

    private void hideLoading() {
        loadingOverlay.setVisibility(View.GONE);
    }

    private void showError(String title, String message) {
        TextView errorTitle = findViewById(R.id.error_title);
        errorTitle.setText(title);
        errorMessage.setText(message);
        errorOverlay.setVisibility(View.VISIBLE);
        loadingOverlay.setVisibility(View.GONE);
    }

    private void hideError() {
        errorOverlay.setVisibility(View.GONE);
    }

    private void showInfoCard() {
        infoCard.setVisibility(View.VISIBLE);
    }
}
