package com.example.project.services;

import android.content.Context;
import android.util.Log;

import com.example.project.utils.ConfigManager;
import com.google.android.gms.maps.model.LatLng;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * Service for handling Google Directions API calls
 */
public class DirectionsService {
    private static final String TAG = "DirectionsService";
    private static final String DIRECTIONS_API_BASE_URL = "https://maps.googleapis.com/maps/api/directions/json";
    
    private final OkHttpClient client;
    private final String apiKey;
    
    public DirectionsService(Context context) {
        this.client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .build();
        
        // Get API key from config
        ConfigManager configManager = ConfigManager.getInstance(context);
        this.apiKey = configManager.getGoogleMapsApiKey();
    }
    
    /**
     * Interface for handling directions API responses
     */
    public interface DirectionsCallback {
        void onSuccess(DirectionsResult result);
        void onError(String error);
    }
    
    /**
     * Result class for directions API response
     */
    public static class DirectionsResult {
        private final List<LatLng> routePoints;
        private final String distance;
        private final String duration;
        private final String polyline;
        
        public DirectionsResult(List<LatLng> routePoints, String distance, String duration, String polyline) {
            this.routePoints = routePoints;
            this.distance = distance;
            this.duration = duration;
            this.polyline = polyline;
        }
        
        public List<LatLng> getRoutePoints() { return routePoints; }
        public String getDistance() { return distance; }
        public String getDuration() { return duration; }
        public String getPolyline() { return polyline; }
    }
    
    /**
     * Get directions between two points
     */
    public void getDirections(LatLng origin, LatLng destination, DirectionsCallback callback) {
        if (apiKey == null || apiKey.isEmpty() || apiKey.equals("YOUR_GOOGLE_MAPS_API_KEY_HERE")) {
            Log.w(TAG, "Google Maps API key not configured, using mock data");
            // Return mock data for testing
            callback.onSuccess(createMockDirectionsResult(origin, destination));
            return;
        }
        
        // Build the request URL
        HttpUrl.Builder urlBuilder = HttpUrl.parse(DIRECTIONS_API_BASE_URL).newBuilder();
        urlBuilder.addQueryParameter("origin", origin.latitude + "," + origin.longitude);
        urlBuilder.addQueryParameter("destination", destination.latitude + "," + destination.longitude);
        urlBuilder.addQueryParameter("mode", "driving");
        urlBuilder.addQueryParameter("key", apiKey);
        
        String url = urlBuilder.build().toString();
        Log.d(TAG, "Directions API URL: " + url);
        
        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();
        
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "Directions API request failed", e);
                callback.onError("Network error: " + e.getMessage());
            }
            
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (!response.isSuccessful()) {
                    Log.e(TAG, "Directions API request unsuccessful: " + response.code());
                    callback.onError("API error: " + response.code());
                    return;
                }
                
                String responseBody = response.body().string();
                Log.d(TAG, "Directions API response received");
                
                try {
                    DirectionsResult result = parseDirectionsResponse(responseBody);
                    callback.onSuccess(result);
                } catch (Exception e) {
                    Log.e(TAG, "Error parsing directions response", e);
                    callback.onError("Error parsing response: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * Parse the Google Directions API JSON response
     * This is a simplified parser - in production you'd use a proper JSON library
     */
    private DirectionsResult parseDirectionsResponse(String jsonResponse) {
        // For now, return mock data
        // In production, you'd parse the actual JSON response
        Log.d(TAG, "Parsing directions response (using mock data for now)");
        
        // Mock route points (you'd extract these from the response polyline)
        List<LatLng> routePoints = new ArrayList<>();
        // Add some sample points for demonstration
        routePoints.add(new LatLng(43.6532, -79.3832));
        routePoints.add(new LatLng(43.6542, -79.3842));
        routePoints.add(new LatLng(43.6552, -79.3852));
        
        return new DirectionsResult(
            routePoints,
            "2.5 km",
            "8 min",
            "mock_polyline_data"
        );
    }
    
    /**
     * Create mock directions result for testing when API key is not configured
     */
    private DirectionsResult createMockDirectionsResult(LatLng origin, LatLng destination) {
        List<LatLng> routePoints = new ArrayList<>();

        // Create a more realistic route with intermediate points
        double latDiff = destination.latitude - origin.latitude;
        double lngDiff = destination.longitude - origin.longitude;

        // Add origin
        routePoints.add(origin);

        // Add intermediate points to simulate a road route (not straight line)
        int numPoints = 8; // More points for smoother route
        for (int i = 1; i < numPoints; i++) {
            double fraction = (double) i / numPoints;

            // Add some curve to make it look more like a real route
            double curveFactor = Math.sin(fraction * Math.PI) * 0.001; // Small curve

            LatLng intermediatePoint = new LatLng(
                origin.latitude + (latDiff * fraction) + curveFactor,
                origin.longitude + (lngDiff * fraction) + (curveFactor * 0.5)
            );
            routePoints.add(intermediatePoint);
        }

        // Add destination
        routePoints.add(destination);

        // Calculate approximate distance
        double distance = calculateDistance(origin, destination);
        String distanceText = String.format("%.1f km", distance);

        // Estimate duration (assuming 25 km/h average speed in city with traffic)
        int durationMinutes = (int) Math.ceil(distance * 2.4); // 2.4 minutes per km
        String durationText = durationMinutes + " min";

        Log.d(TAG, "Created mock route with " + routePoints.size() + " points, distance: " + distanceText + ", duration: " + durationText);

        return new DirectionsResult(routePoints, distanceText, durationText, "mock_polyline");
    }
    
    /**
     * Calculate approximate distance between two points using Haversine formula
     */
    private double calculateDistance(LatLng point1, LatLng point2) {
        final int R = 6371; // Radius of the earth in km
        
        double latDistance = Math.toRadians(point2.latitude - point1.latitude);
        double lonDistance = Math.toRadians(point2.longitude - point1.longitude);
        
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(point1.latitude)) * Math.cos(Math.toRadians(point2.latitude))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return R * c; // Distance in km
    }
    
    /**
     * Geocode an address to get coordinates
     * This is a simplified version - in production you'd use Google Geocoding API
     */
    public void geocodeAddress(String address, GeocodeCallback callback) {
        Log.d(TAG, "Geocoding address: " + address);
        
        // For now, return a mock location based on the address
        // In production, you'd call Google Geocoding API
        LatLng mockLocation = getMockLocationForAddress(address);
        callback.onSuccess(mockLocation);
    }
    
    /**
     * Interface for geocoding callbacks
     */
    public interface GeocodeCallback {
        void onSuccess(LatLng location);
        void onError(String error);
    }
    
    /**
     * Get a mock location for testing purposes
     */
    private LatLng getMockLocationForAddress(String address) {
        // Use the specific coordinates provided: 43.66983408079368, -79.38376633865508
        // This appears to be in Toronto area
        return new LatLng(43.66983408079368, -79.38376633865508);
    }
}
