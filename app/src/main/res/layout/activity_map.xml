<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/map_coordinator_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    tools:context=".MapActivity">

    <!-- Main Map Container -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- Top App Bar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/background_primary"
            android:elevation="4dp"
            app:title="Directions"
            app:titleTextAppearance="@style/TextAppearance.Foodie.AppTitle"
            app:titleTextColor="@color/text_primary"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:navigationIconTint="@color/text_primary" />

        <!-- Map Container -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <!-- Google Map Fragment -->
            <fragment
                android:id="@+id/map_fragment"
                android:name="com.google.android.gms.maps.SupportMapFragment"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <!-- Loading Overlay -->
            <LinearLayout
                android:id="@+id/loading_overlay"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/background_primary"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="visible">

                <ProgressBar
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginBottom="16dp"
                    android:indeterminateTint="@color/colorPrimary" />

                <TextView
                    android:id="@+id/loading_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Loading map..."
                    android:textAppearance="@style/TextAppearance.Foodie.RestaurantDetails"
                    android:textColor="@color/text_secondary" />

            </LinearLayout>

            <!-- Error Overlay -->
            <LinearLayout
                android:id="@+id/error_overlay"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/background_primary"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="32dp"
                android:visibility="gone">

                <ImageView
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:layout_marginBottom="16dp"
                    android:src="@drawable/ic_error_outline"
                    app:tint="@color/text_secondary" />

                <TextView
                    android:id="@+id/error_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="Unable to load map"
                    android:textAppearance="@style/TextAppearance.Foodie.RestaurantName"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:id="@+id/error_message"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp"
                    android:gravity="center"
                    android:text="Please check your internet connection and try again."
                    android:textAppearance="@style/TextAppearance.Foodie.RestaurantDetails"
                    android:textColor="@color/text_secondary" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/retry_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Retry"
                    android:textAppearance="@style/TextAppearance.Foodie.ActionButton"
                    app:backgroundTint="@color/colorPrimary"
                    app:cornerRadius="8dp" />

            </LinearLayout>

        </FrameLayout>

        <!-- Bottom Info Card - Fixed at bottom -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/info_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:visibility="gone"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Restaurant Info -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="12dp"
                    android:src="@drawable/ic_restaurant"
                    app:tint="@color/colorPrimary" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/restaurant_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Restaurant Name"
                        android:textAppearance="@style/TextAppearance.Foodie.RestaurantName"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:id="@+id/restaurant_address"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Restaurant Address"
                        android:textAppearance="@style/TextAppearance.Foodie.RestaurantDetails"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

            </LinearLayout>

            <!-- Route Info -->
            <LinearLayout
                android:id="@+id/route_info_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="gone">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="12dp"
                    android:src="@drawable/ic_directions"
                    app:tint="@color/colorPrimary" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/route_distance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="16dp"
                        android:text="--"
                        android:textAppearance="@style/TextAppearance.Foodie.RestaurantDetails"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:id="@+id/route_duration"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="--"
                        android:textAppearance="@style/TextAppearance.Foodie.RestaurantDetails"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
